<?php

namespace App\Services;


use Exception;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON><PERSON>\Rest\Client;

class TwilioSMSService
{
    /**
     * Sends sms to user using <PERSON>wilio's programmable sms client
     * @param string $message Body of sms
     * @param Number $recipients string of phone number of recepient
     */
    public static function sendMessage($message, $mobileNo)
    {
        $enableSmsApi = config("app.enable_sms_api");
        $env = strtolower(config('app.env'));
        try {
            $foundTestNumber = false;
            $isTestingENV = in_array($env,[ENV_SLIMMED_LOCAL,ENV_SLIMMED_DEV,ENV_SLIMMED_TEST,ENV_SLIMMED_STAGING]);

            if ($enableSmsApi == 0 && $isTestingENV) {
                $testNumbers = config("app.test_numbers");
                if (!empty($testNumbers) && $testNumbers != '' && $testNumbers != null) {
                    $testNumbersList = explode(",", $testNumbers);
                    if (count($testNumbersList) > 0) {
                        if (in_array($mobileNo, $testNumbersList)) {
                            $foundTestNumber = true;
                        }
                    }
                }
            }
            if ($isTestingENV && !$foundTestNumber) {
                LogService::info('slack', 'SMS', $message . " - Number : " . $mobileNo);
            }
            if($enableSmsApi == 1 ||  $foundTestNumber){
                $account_sid = config("app.twilio_sid");
                $auth_token = config("app.twilio_auth_token");
                $twilio_number = config("app.twilio_number");
                $twilio_messaging_service_sid = config("app.twilio_messaging_service_sid");

                if(empty($account_sid) || empty($auth_token) || empty($twilio_number) || empty($twilio_messaging_service_sid))
                    throw new Exception(MESSAGE_ENV_VALUES_FOR_TWILIO_NOT_FOUND);
                $client = new Client($account_sid, $auth_token);

                $result = $client->messages->create(
                    $mobileNo,
                    ['from' => $twilio_number, 'body' => $message,'messagingServiceSid' => $twilio_messaging_service_sid]
                );
                if (isset($result->status) && !in_array($result->status,['success','accepted'])) {
                    if ($result->warnings){
                        throw new Exception(MESSAGE_SMS_SENDING_FAILED. $result->warnings[0]->message);
                    }
                    else{
                        throw new Exception(MESSAGE_SMS_SENDING_FAILED. $result->errors[0]->message);
                    }
                } else if (isset($result->IsError) && $result->IsError == true) {
                    throw new Exception(MESSAGE_SMS_SENDING_FAILED. $result->ErrorMessage);
                }
            }
            return ['success' => true, 'message' => MESSAGE_SMS_SENT_SUCCESSFULLY];
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            return ['status' => false, 'message' => $ex->getMessage()];
        }
    }
}
